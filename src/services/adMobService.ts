import mobileAds, { 
  MaxAd<PERSON>ontentRating, 
  BannerAd, 
  BannerAdSize, 
  TestIds,
  InterstitialAd,
  AdEventType,
  RewardedAd,
  RewardedAdEventType
} from 'react-native-google-mobile-ads';
import { Platform } from 'react-native';

// AdMob Configuration
export const ADMOB_CONFIG = {
  APP_ID: 'ca-app-pub-2252367109344431~5239686489',
  
  // Test Ad Unit IDs (use these during development)
  TEST_AD_UNITS: {
    BANNER: TestIds.BANNER,
    INTERSTITIAL: TestIds.INTERSTITIAL,
    REWARDED: TestIds.REWARDED,
  },
  
  // Production Ad Unit IDs (replace with your actual ad unit IDs from AdMob console)
  PRODUCTION_AD_UNITS: {
    BANNER: Platform.OS === 'ios' 
      ? 'ca-app-pub-2252367109344431/XXXXXXXXXX' // Replace with your iOS banner ad unit ID
      : 'ca-app-pub-2252367109344431/XXXXXXXXXX', // Replace with your Android banner ad unit ID
    INTERSTITIAL: Platform.OS === 'ios'
      ? 'ca-app-pub-2252367109344431/XXXXXXXXXX' // Replace with your iOS interstitial ad unit ID
      : 'ca-app-pub-2252367109344431/XXXXXXXXXX', // Replace with your Android interstitial ad unit ID
    REWARDED: Platform.OS === 'ios'
      ? 'ca-app-pub-2252367109344431/XXXXXXXXXX' // Replace with your iOS rewarded ad unit ID
      : 'ca-app-pub-2252367109344431/XXXXXXXXXX', // Replace with your Android rewarded ad unit ID
  }
};

// Use test ads during development, production ads in release
const isDevelopment = __DEV__;

export const AD_UNITS = isDevelopment 
  ? ADMOB_CONFIG.TEST_AD_UNITS 
  : ADMOB_CONFIG.PRODUCTION_AD_UNITS;

class AdMobService {
  private isInitialized = false;
  private interstitialAd: InterstitialAd | null = null;
  private rewardedAd: RewardedAd | null = null;

  /**
   * Initialize AdMob
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        console.log('[AdMob] Already initialized');
        return;
      }

      console.log('[AdMob] Initializing...');
      
      // Initialize the Google Mobile Ads SDK
      await mobileAds().initialize();

      // Configure AdMob settings
      await mobileAds().setRequestConfiguration({
        // Set maximum ad content rating
        maxAdContentRating: MaxAdContentRating.PG,
        
        // Indicate that you want your content treated as child-directed for purposes of COPPA
        tagForChildDirectedTreatment: false,
        
        // Indicate that you want the ad request to be handled in a manner suitable for users under the age of consent
        tagForUnderAgeOfConsent: false,
        
        // Test device IDs (add your test device IDs here)
        testDeviceIdentifiers: isDevelopment ? ['EMULATOR'] : [],
      });

      this.isInitialized = true;
      console.log('[AdMob] Initialized successfully');
      
      // Pre-load interstitial and rewarded ads
      this.loadInterstitialAd();
      this.loadRewardedAd();
      
    } catch (error) {
      console.error('[AdMob] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Check if AdMob is initialized
   */
  isAdMobInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Load interstitial ad
   */
  private loadInterstitialAd(): void {
    try {
      this.interstitialAd = InterstitialAd.createForAdRequest(AD_UNITS.INTERSTITIAL);
      
      this.interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
        console.log('[AdMob] Interstitial ad loaded');
      });

      this.interstitialAd.addAdEventListener(AdEventType.ERROR, (error) => {
        console.error('[AdMob] Interstitial ad error:', error);
      });

      this.interstitialAd.load();
    } catch (error) {
      console.error('[AdMob] Failed to load interstitial ad:', error);
    }
  }

  /**
   * Show interstitial ad
   */
  async showInterstitialAd(): Promise<void> {
    try {
      if (this.interstitialAd?.loaded) {
        await this.interstitialAd.show();
        // Reload the ad for next time
        this.loadInterstitialAd();
      } else {
        console.log('[AdMob] Interstitial ad not ready');
      }
    } catch (error) {
      console.error('[AdMob] Failed to show interstitial ad:', error);
    }
  }

  /**
   * Load rewarded ad
   */
  private loadRewardedAd(): void {
    try {
      this.rewardedAd = RewardedAd.createForAdRequest(AD_UNITS.REWARDED);
      
      this.rewardedAd.addAdEventListener(RewardedAdEventType.LOADED, () => {
        console.log('[AdMob] Rewarded ad loaded');
      });

      this.rewardedAd.addAdEventListener(RewardedAdEventType.ERROR, (error) => {
        console.error('[AdMob] Rewarded ad error:', error);
      });

      this.rewardedAd.load();
    } catch (error) {
      console.error('[AdMob] Failed to load rewarded ad:', error);
    }
  }

  /**
   * Show rewarded ad
   */
  async showRewardedAd(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (this.rewardedAd?.loaded) {
          let rewarded = false;

          this.rewardedAd.addAdEventListener(RewardedAdEventType.EARNED_REWARD, (reward) => {
            console.log('[AdMob] User earned reward:', reward);
            rewarded = true;
          });

          this.rewardedAd.addAdEventListener(AdEventType.CLOSED, () => {
            // Reload the ad for next time
            this.loadRewardedAd();
            resolve(rewarded);
          });

          this.rewardedAd.show();
        } else {
          console.log('[AdMob] Rewarded ad not ready');
          resolve(false);
        }
      } catch (error) {
        console.error('[AdMob] Failed to show rewarded ad:', error);
        resolve(false);
      }
    });
  }

  /**
   * Get banner ad unit ID
   */
  getBannerAdUnitId(): string {
    return AD_UNITS.BANNER;
  }
}

export default new AdMobService();
